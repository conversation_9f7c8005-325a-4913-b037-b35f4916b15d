import { useState } from "react";
import { Table } from "@radix-ui/themes";
import { TransactionDetailsDTO, TransactionDTO } from "@/api/data-contracts";
import { InfoLayout } from "@repo/ui/info-layout";
import { getCommonPinningStyles } from "@repo/ui/utils/table-utils";

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import style from "./index.module.scss";
import { useTransactionColumns } from "./use-transaction-columns";
import { TransactionDetailsDialog } from "./transaction-details-dialog";
import service from "@/api";

export const TransactionTable = ({
  data,
  disableActions = false,
}: {
  data: TransactionDTO[];
  disableActions?: boolean;
}) => {
  const [selectedTransaction, setSelectedTransaction] =
    useState<TransactionDetailsDTO | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleViewDetails = async (id: string) => {
    const data = await service.getTransactionDetails(id);

    if (data.data?.data) {
      setSelectedTransaction(data.data.data);
      setDialogOpen(true);
    }
  };

  const columns = useTransactionColumns({
    onViewDetails: handleViewDetails,
    disableActions,
  });

  const table = useReactTable({
    data,
    columns,
    state: { columnPinning: { right: ["action"] } },
    getCoreRowModel: getCoreRowModel(),
  });

  if (!data.length) {
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10"
          icon="/graphics/orange/empty-file.png"
          iconAlt="no data"
          title={"No transactions found"}
          description={
            "Transactions made via Fiat and Cryptocurrency will appear here"
          }
        />
      </div>
    );
  }

  return (
    <>
      <Table.Root
        variant="surface"
        size="3"
        layout="fixed"
        className={style.override}
      >
        <Table.Header>
          {table.getHeaderGroups().map((headerGroup) => (
            <Table.Row align="center" key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Table.ColumnHeaderCell
                  key={header.id}
                  style={{ ...getCommonPinningStyles(header.column, true) }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </Table.ColumnHeaderCell>
              ))}
            </Table.Row>
          ))}
        </Table.Header>
        <Table.Body>
          {table.getRowModel().rows.map((row) => (
            <Table.Row align="center" key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <Table.Cell
                  key={cell.id}
                  style={{ ...getCommonPinningStyles(cell.column) }}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
      <TransactionDetailsDialog
        data={selectedTransaction || undefined}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
      />
    </>
  );
};
