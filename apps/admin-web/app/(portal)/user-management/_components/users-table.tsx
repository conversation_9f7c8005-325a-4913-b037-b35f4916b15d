import { Table } from "@radix-ui/themes";
import { UserSummaryResponse } from "@/api/data-contracts";
import { InfoLayout } from "@repo/ui/info-layout";
import { getCommonPinningStyles } from "@repo/ui/utils/table-utils";

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import style from "./index.module.scss";
import { useUsersColumns } from "./use-users-columns";

export const UsersTable = ({ data }: { data: UserSummaryResponse[] }) => {
  const columns = useUsersColumns();

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (!data.length) {
    return (
      <div className="bg-[#********] rounded-lg">
        <InfoLayout
          className="py-10"
          icon="/empty-file.png"
          iconAlt="no data"
          title="No users found"
          description="Users will appear here when they register and create accounts"
        />
      </div>
    );
  }

  return (
    <Table.Root
      variant="surface"
      size="3"
      layout="fixed"
      className={style.override}
    >
      <Table.Header>
        {table.getHeaderGroups().map((headerGroup) => (
          <Table.Row align="center" key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <Table.ColumnHeaderCell
                key={header.id}
                style={{ ...getCommonPinningStyles(header.column, true) }}
                className="text-nowrap"
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </Table.ColumnHeaderCell>
            ))}
          </Table.Row>
        ))}
      </Table.Header>
      <Table.Body>
        {table.getRowModel().rows.map((row) => (
          <Table.Row align="center" key={row.id}>
            {row.getVisibleCells().map((cell) => (
              <Table.Cell
                key={cell.id}
                style={{ ...getCommonPinningStyles(cell.column) }}
              >
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </Table.Cell>
            ))}
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  );
};
