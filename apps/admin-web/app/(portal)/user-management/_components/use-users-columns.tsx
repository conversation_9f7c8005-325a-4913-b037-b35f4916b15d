import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { UserSummaryResponse } from "@/api/data-contracts";
import { Text } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";

const columnHelper = createColumnHelper<UserSummaryResponse>();

export const useUsersColumns = () => {
  const columns: ColumnDef<UserSummaryResponse, any>[] = [
    columnHelper.accessor("publicId", {
      header: "Portfolio ID",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text size="3" color="orange" className="text-nowrap">
              {value || "-"}
            </Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("email", {
      header: "Email",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text size="3">{value || "-"}</Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("createdAt", {
      header: "Account creation time",
      cell: (info) => {
        const value = info.getValue();
        if (!value) return <Text size="3">-</Text>;

        const date = new Date(value);
        const formattedDate = date.toLocaleString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        });

        return <Text size="3">{formattedDate}</Text>;
      },
      size: 200,
    }),
    columnHelper.accessor("totalInvestment", {
      header: "Total invested amount (USD)",
      cell: (info) => {
        const value = info.getValue();
        if (!value?.amount) {
          return <Text size="3">-</Text>;
        }

        const amount = parseFloat(value.amount);
        const formattedAmount = amount.toLocaleString("en-US", {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        });

        return (
          <Text size="3" color="green" weight="medium">
            +{formattedAmount}
          </Text>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("countryOfResidence", {
      header: "Country of residence",
      cell: (info) => {
        const value = info.getValue();
        return <Text size="3">{value || "-"}</Text>;
      },
      size: 180,
    }),
  ];

  return columns;
};
